#!/bin/bash

# 清除 Next.js 构建缓存
echo "Cleaning Next.js build cache..."
rm -rf apps/web/.next
rm -rf apps/h5/.next

# 清除 Turbo 缓存
echo "Cleaning Turbo cache..."
rm -rf .turbo
rm -rf node_modules/.cache/turbo

# 清除 node_modules
echo "Cleaning node_modules..."
rm -rf node_modules
rm -rf apps/*/node_modules
rm -rf packages/*/node_modules

# 清除 PM2 缓存
echo "Cleaning PM2 cache..."
rm -rf apps/web/deploy/.pm2
rm -rf apps/h5/deploy/.pm2

# 清除 TypeScript 缓存
echo "Cleaning TypeScript cache..."
rm -rf apps/**/*.tsbuildinfo
rm -rf packages/**/*.tsbuildinfo

# 清除 Redis 缓存(如果 Redis 在运行)
if command -v redis-cli &> /dev/null; then
    echo "Cleaning Redis cache..."
    redis-cli FLUSHALL
fi

echo "Cache cleanup completed!"
